package com.xlc.excel.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.xlc.excel.dto.ExcelExportRequest;
import com.xlc.excel.enums.ExcelTemplateEnum;
import com.xlc.excel.exception.ExcelException;
import com.xlc.excel.service.ExcelExportService;
import com.xlc.excel.utils.ExcelUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Supplier;

/**
 * Excel导出服务实现类
 * 
 * <AUTHOR>
 * @since 2025-06-17
 */
@Service
public class ExcelExportServiceImpl implements ExcelExportService {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelExportServiceImpl.class);
    
    /**
     * 异步执行器
     */
    private final Executor asyncExecutor = Executors.newFixedThreadPool(5);
    
    @Override
    public <T> ExcelExportResult exportExcel(ExcelExportRequest<T> request) {
        logger.info("开始导出Excel文件：{}", request.getFileName());
        
        ExcelExportResult result = new ExcelExportResult();
        result.setExportStarted();
        result.setFileName(request.getFileName());
        
        try {
            // 验证请求参数
            request.validate();
            
            // 根据导出模式选择不同的处理方式
            switch (request.getExportMode()) {
                case NORMAL:
                    return exportNormal(request);
                case PAGED:
                    return exportPaged(request);
                case STREAMING:
                    return exportStreaming(request);
                case TEMPLATE:
                    return exportTemplate(request);
                default:
                    throw new ExcelException("UNSUPPORTED_EXPORT_MODE", "不支持的导出模式：" + request.getExportMode());
            }
            
        } catch (ExcelException e) {
            logger.error("Excel导出失败：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setExportCompleted();
            return result;
        } catch (Exception e) {
            logger.error("Excel导出过程中发生未知异常：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage("Excel导出失败：" + e.getMessage());
            result.setExportCompleted();
            return result;
        }
    }
    
    /**
     * 普通模式导出
     */
    private <T> ExcelExportResult exportNormal(ExcelExportRequest<T> request) {
        ExcelExportResult result = new ExcelExportResult();
        result.setExportStarted();
        result.setFileName(request.getFileName());
        
        try {
            List<T> data = request.getData();
            if (data == null || data.isEmpty()) {
                logger.warn("导出数据为空：{}", request.getFileName());
                data = List.of(); // 创建空列表
            }
            
            // 检查数据量限制
            if (request.getMaxExportRows() > 0 && data.size() > request.getMaxExportRows()) {
                throw new ExcelException("DATA_SIZE_EXCEEDED", 
                        String.format("数据量超过限制：%d > %d", data.size(), request.getMaxExportRows()));
            }
            
            // 创建ExcelWriter
            ExcelWriter excelWriter = EasyExcel.write(request.getOutputStream(), request.getDataClass())
                    .autoCloseStream(false) // 不自动关闭流
                    .build();
            
            try {
                // 创建WriteSheet
                WriteSheet writeSheet = EasyExcel.writerSheet(request.getEffectiveSheetName())
                        .build();
                
                // 写入数据
                excelWriter.write(data, writeSheet);
                
                // 设置结果
                result.setSuccess(true);
                result.setTotalRows(data.size());
                result.setTotalSheets(1);
                result.setMessage("导出成功");
                
                logger.info("Excel文件导出成功：{}，数据行数：{}", request.getFileName(), data.size());
                
            } finally {
                // 千万别忘记finish 会帮忙关闭流
                excelWriter.finish();
            }
            
        } catch (Exception e) {
            logger.error("普通模式导出失败：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        result.setExportCompleted();
        return result;
    }
    
    /**
     * 分页模式导出
     */
    private <T> ExcelExportResult exportPaged(ExcelExportRequest<T> request) {
        ExcelExportResult result = new ExcelExportResult();
        result.setExportStarted();
        result.setFileName(request.getFileName());
        
        try {
            // 分页导出逻辑
            // 这里需要根据实际业务需求实现分页数据获取
            logger.info("分页模式导出暂未完全实现，使用普通模式导出");
            return exportNormal(request);
            
        } catch (Exception e) {
            logger.error("分页模式导出失败：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setExportCompleted();
            return result;
        }
    }
    
    /**
     * 流式模式导出
     */
    private <T> ExcelExportResult exportStreaming(ExcelExportRequest<T> request) {
        ExcelExportResult result = new ExcelExportResult();
        result.setExportStarted();
        result.setFileName(request.getFileName());
        
        try {
            // 流式导出逻辑
            // 这里需要根据实际业务需求实现流式数据处理
            logger.info("流式模式导出暂未完全实现，使用普通模式导出");
            return exportNormal(request);
            
        } catch (Exception e) {
            logger.error("流式模式导出失败：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setExportCompleted();
            return result;
        }
    }
    
    /**
     * 模板模式导出
     */
    private <T> ExcelExportResult exportTemplate(ExcelExportRequest<T> request) {
        ExcelExportResult result = new ExcelExportResult();
        result.setExportStarted();
        result.setFileName(request.getFileName());
        
        try {
            // 模板导出逻辑
            // 这里可以实现基于模板的导出功能
            logger.info("模板模式导出暂未完全实现，使用普通模式导出");
            return exportNormal(request);
            
        } catch (Exception e) {
            logger.error("模板模式导出失败：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setExportCompleted();
            return result;
        }
    }
    
    @Override
    public <T> ExcelExportResult exportByTableName(OutputStream outputStream, String fileName, String tableName, List<T> data, Class<T> dataClass) {
        ExcelExportRequest<T> request = ExcelExportRequest.byTableName(outputStream, fileName, tableName, data, dataClass);
        return exportExcel(request);
    }
    
    @Override
    public <T> ExcelExportResult exportByTemplate(OutputStream outputStream, String fileName, ExcelTemplateEnum template, List<T> data, Class<T> dataClass) {
        ExcelExportRequest<T> request = ExcelExportRequest.byTemplate(outputStream, fileName, template, data, dataClass);
        return exportExcel(request);
    }
    
    @Override
    public <T> ExcelExportResult exportPaged(OutputStream outputStream, String fileName, String tableName, Supplier<List<T>> dataSupplier, Class<T> dataClass, int pageSize) {
        ExcelExportRequest<T> request = ExcelExportRequest.pagedExport(outputStream, fileName, tableName, dataClass, pageSize);
        
        // 这里需要实现分页数据获取逻辑
        // 暂时使用一次性获取所有数据的方式
        List<T> data = dataSupplier.get();
        request.setData(data);
        
        return exportExcel(request);
    }
    
    @Override
    public <T> ExcelExportResult exportStreaming(OutputStream outputStream, String fileName, String tableName, Supplier<List<T>> dataSupplier, Class<T> dataClass) {
        ExcelExportRequest<T> request = ExcelExportRequest.streamingExport(outputStream, fileName, tableName, dataClass);
        
        // 这里需要实现流式数据获取逻辑
        // 暂时使用一次性获取所有数据的方式
        List<T> data = dataSupplier.get();
        request.setData(data);
        
        return exportExcel(request);
    }
    
    @Override
    public <T> CompletableFuture<ExcelExportResult> exportExcelAsync(ExcelExportRequest<T> request) {
        logger.info("开始异步导出Excel文件：{}", request.getFileName());
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return exportExcel(request);
            } catch (Exception e) {
                logger.error("异步导出Excel文件失败：{}", e.getMessage(), e);
                throw new RuntimeException(e);
            }
        }, asyncExecutor);
    }
    
    @Override
    public ExcelExportResult exportTemplate(OutputStream outputStream, String fileName, ExcelTemplateEnum template) {
        logger.info("开始导出Excel模板文件：{}，模板：{}", fileName, template.getName());
        
        ExcelExportResult result = new ExcelExportResult();
        result.setExportStarted();
        result.setFileName(fileName);
        
        try {
            // 创建空的数据列表用于生成模板
            List<Object> emptyData = List.of();
            
            // 根据模板类型确定数据类型
            Class<?> templateClass = getTemplateClass(template);
            
            // 创建ExcelWriter
            ExcelWriter excelWriter = EasyExcel.write(outputStream, templateClass)
                    .autoCloseStream(false)
                    .build();
            
            try {
                // 创建WriteSheet
                WriteSheet writeSheet = EasyExcel.writerSheet(template.getName())
                        .build();
                
                // 写入空数据（只生成表头）
                excelWriter.write(emptyData, writeSheet);
                
                result.setSuccess(true);
                result.setTotalRows(0);
                result.setTotalSheets(1);
                result.setMessage("模板导出成功");
                
                logger.info("Excel模板文件导出成功：{}", fileName);
                
            } finally {
                excelWriter.finish();
            }
            
        } catch (Exception e) {
            logger.error("导出Excel模板文件失败：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        result.setExportCompleted();
        return result;
    }
    
    @Override
    public <T> ExcelExportResult exportTemplate(OutputStream outputStream, String fileName, String tableName, Class<T> dataClass) {
        logger.info("开始导出Excel模板文件：{}，表名：{}", fileName, tableName);
        
        ExcelExportResult result = new ExcelExportResult();
        result.setExportStarted();
        result.setFileName(fileName);
        
        try {
            // 创建空的数据列表用于生成模板
            List<T> emptyData = List.of();
            
            // 创建ExcelWriter
            ExcelWriter excelWriter = EasyExcel.write(outputStream, dataClass)
                    .autoCloseStream(false)
                    .build();
            
            try {
                // 创建WriteSheet
                WriteSheet writeSheet = EasyExcel.writerSheet(tableName)
                        .build();
                
                // 写入空数据（只生成表头）
                excelWriter.write(emptyData, writeSheet);
                
                result.setSuccess(true);
                result.setTotalRows(0);
                result.setTotalSheets(1);
                result.setMessage("模板导出成功");
                
                logger.info("Excel模板文件导出成功：{}", fileName);
                
            } finally {
                excelWriter.finish();
            }
            
        } catch (Exception e) {
            logger.error("导出Excel模板文件失败：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        result.setExportCompleted();
        return result;
    }
    
    @Override
    public ExcelExportResult exportMultiSheet(OutputStream outputStream, String fileName, Map<String, ExcelExportRequest<?>> sheetDataMap) {
        logger.info("开始导出多工作表Excel文件：{}，工作表数量：{}", fileName, sheetDataMap.size());
        
        ExcelExportResult result = new ExcelExportResult();
        result.setExportStarted();
        result.setFileName(fileName);
        
        try {
            if (sheetDataMap == null || sheetDataMap.isEmpty()) {
                throw new ExcelException("INVALID_PARAMETER", "工作表数据不能为空");
            }
            
            // 创建ExcelWriter（不指定具体的数据类型）
            ExcelWriter excelWriter = EasyExcel.write(outputStream)
                    .autoCloseStream(false)
                    .build();
            
            try {
                int totalRows = 0;
                int sheetIndex = 0;
                
                for (Map.Entry<String, ExcelExportRequest<?>> entry : sheetDataMap.entrySet()) {
                    String sheetName = entry.getKey();
                    ExcelExportRequest<?> sheetRequest = entry.getValue();
                    
                    logger.info("写入工作表：{}", sheetName);
                    
                    // 创建WriteSheet
                    WriteSheet writeSheet = EasyExcel.writerSheet(sheetIndex, sheetName)
                            .head(sheetRequest.getDataClass())
                            .build();
                    
                    // 写入数据
                    List<?> data = sheetRequest.getData();
                    if (data != null) {
                        excelWriter.write(data, writeSheet);
                        totalRows += data.size();
                    }
                    
                    sheetIndex++;
                }
                
                result.setSuccess(true);
                result.setTotalRows(totalRows);
                result.setTotalSheets(sheetDataMap.size());
                result.setMessage("多工作表导出成功");
                
                logger.info("多工作表Excel文件导出成功：{}，总行数：{}", fileName, totalRows);
                
            } finally {
                excelWriter.finish();
            }
            
        } catch (Exception e) {
            logger.error("导出多工作表Excel文件失败：{}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
        }
        
        result.setExportCompleted();
        return result;
    }
    
    /**
     * 根据模板获取对应的数据类型
     */
    private Class<?> getTemplateClass(ExcelTemplateEnum template) {
        // 这里需要根据实际的模板定义返回对应的数据类型
        // 暂时返回Object类型
        switch (template) {
            case USER_INFO:
                // return UserInfo.class;
                break;
            case PRODUCT_INFO:
                // return ProductInfo.class;
                break;
            case ORDER_INFO:
                // return OrderInfo.class;
                break;
            case EMPLOYEE_INFO:
                // return EmployeeInfo.class;
                break;
            case DEPARTMENT_INFO:
                // return DepartmentInfo.class;
                break;
            default:
                break;
        }
        
        return Object.class;
    }
}
